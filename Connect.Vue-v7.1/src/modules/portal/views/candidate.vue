<template>
	<div class="wrapper">
		<el-row class="opt">
			<el-col :span="10">
				<el-button @click="showAddDialog" type="primary" style="margin: 5px 0;">添加</el-button>
			</el-col>
			<el-col :span="14" style="text-align: right; display: flex; align-items: center; justify-content: flex-end; gap: 10px;">
				<el-input
					placeholder="请输入标题或URL"
					v-model="search.s"
					style="width: 300px"
					clearable
					@clear="refresh"
				>
					<template #append>
						<el-button :icon="Search" @click="refresh" />
					</template>
				</el-input>
			</el-col>
		</el-row>
		<el-row class="list">
			<el-col :span="24">
				<el-table
					border
					style="width: 100%"
					:data="tableData"
					:max-height="tableHeight"
					:row-style="{ height: '40px' }"
					:header-cell-style="{ background: '#ebeef5', color: '#333' }"
				>
					<el-table-column fixed prop="id" label="ID" width="70" align="center">
					</el-table-column>
					<el-table-column label="审核" width="85" align="center">
						<template #default="scope">
							<el-tag :type="scope.row.approvedBy ? 'success' : 'warning'">
								{{ scope.row.approvedBy ? '已审核' : '待审核' }}
							</el-tag>
						</template>
					</el-table-column>
					<el-table-column label="Url" width="240" align="left" :show-overflow-tooltip="true">
						<template #default="scope">
							<el-link :href="scope.row.url" target="_blank" type="primary" v-if="scope.row.url">
								{{ getUrlDisplayText(scope.row) }}
							</el-link>
							<span v-else>-</span>
						</template>
					</el-table-column>
					<el-table-column label="标题" width="250" align="left" :show-overflow-tooltip="true">
						<template #default="scope">
							<el-link
								v-if="scope.row.wwwArticleID"
								:href="`https://www.chasedream.com/article/${scope.row.wwwArticleID}`"
								target="_blank"
								type="primary"
							>
								{{ scope.row.title || '-' }}
							</el-link>
							<span v-else>{{ scope.row.title || '-' }}</span>
						</template>
					</el-table-column>
					<el-table-column label="作者" width="100" align="center">
						<template #default="scope">
							{{ scope.row.author || '-' }}
						</template>
					</el-table-column>
					<el-table-column label="频道" width="120" align="center">
						<template #default="scope">
							{{ scope.row.channel || '-' }}
						</template>
					</el-table-column>
					<el-table-column label="标签" width="120" align="center">
						<template #default="scope">
							{{ scope.row.tag || '-' }}
						</template>
					</el-table-column>
					<el-table-column label="添加人/审核人" width="120" align="center">
						<template #default="scope">
							<div>
								<div v-if="scope.row.addedBy">{{ scope.row.addedBy }}</div>
								<div v-if="scope.row.approvedBy" style="color: #67c23a;">{{ scope.row.approvedBy }}</div>
								<span v-if="!scope.row.addedBy && !scope.row.approvedBy">-</span>
							</div>
						</template>
					</el-table-column>
					<el-table-column label="审核时间" width="150" align="center">
						<template #default="scope">
							{{ scope.row.approvedTime ? formatter(scope.row.approvedTime) : '-' }}
						</template>
					</el-table-column>
					<el-table-column label="创建时间" width="150" align="center">
						<template #default="scope">
							{{ scope.row.createTime ? formatter(scope.row.createTime) : '-' }}
						</template>
					</el-table-column>
					<el-table-column fixed="right" label="操作" width="100" align="center">
						<template #default="scope">
							<el-icon
								@click="editClick(scope.row)"
								class="cursor-pointer"
								style="color: #464bd7; margin-right: 8px"
								:size="20"
								><edit
							/></el-icon>

							<el-popconfirm
										title="确定要删除吗?"
										@confirm="deleteHandler(scope.row)"
									>
										<template #reference>
											<el-icon
												class="cursor-pointer"
												style="color: crimson; margin-right: 8px"
												:size="20"
												><delete
											/></el-icon>
										</template>
									</el-popconfirm>
						</template>
					</el-table-column>
				</el-table>
			</el-col>
		</el-row>
		<el-row class="page">
			<el-pagination
				class="pagination"
				background
				:currentPage="currentPage"
				:page-size="pageSize"
				layout="total, sizes, prev, pager, next, jumper"
				:total="total"
				@size-change="handleSizeChange"
				@current-change="handleCurrentChange"
			/>
		</el-row>

		<!-- 添加/编辑弹出框 -->
		<el-dialog
			v-model="dialogVisible"
			:title="isEdit ? '编辑' : '添加'"
			width="95%"
			:close-on-click-modal="false"
			@close="closeDialog"
		>
			<div class="dialog-container">
				<!-- 左侧编辑区域 -->
				<div class="editor-left">
					<el-form :model="form" label-width="90px" :rules="rules" ref="formRef">
					<el-form-item label="Url" :required="true" prop="url">
						<div class="url-input-group">
							<el-input
								v-model="form.url"
								placeholder="请输入文章URL地址"
								clearable
								class="url-input"
							/>
							<div class="url-buttons">
								<el-button @click="getArticleInfo" :loading="gettingInfo" type="primary">获取</el-button>
								<el-button @click="visitUrl" :icon="Link">访问</el-button>
							</div>
						</div>
					</el-form-item>

				<el-form-item label="标题" :required="true" prop="title">
					<el-input
						v-model="form.title"
						placeholder="请输入文章标题"
						clearable
					/>
				</el-form-item>

				<el-row :gutter="16">
					<el-col :span="12">
						<el-form-item label="版块" prop="board">
							<el-input
								v-model="form.board"
								placeholder="请输入版块"
								clearable
							/>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="创建时间" prop="createTime">
							<el-date-picker
								v-model="form.createTime"
								type="datetime"
								placeholder="选择创建时间"
								format="YYYY-MM-DD HH:mm:ss"
								value-format="YYYY-MM-DD HH:mm:ss"
								style="width: 100%"
							/>
						</el-form-item>
					</el-col>
				</el-row>

				<el-row :gutter="16">
					<el-col :span="12">
						<el-form-item label="作者" prop="author">
							<el-input
								v-model="form.author"
								placeholder="请输入作者"
								clearable
							/>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="频道" prop="channel">
							<el-input
								v-model="form.channel"
								placeholder="请输入频道"
								clearable
							/>
						</el-form-item>
					</el-col>
				</el-row>

				<el-row :gutter="16">
					<el-col :span="12">
						<el-form-item label="标签" prop="tag">
							<el-input
								v-model="form.tag"
								placeholder="请输入标签"
								clearable
							/>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="修复新闻" prop="fixNews">
							<el-switch
								v-model="form.fixNews"
								active-text="是"
								inactive-text="否"
							/>
						</el-form-item>
					</el-col>
				</el-row>

					<el-form-item label="内容" prop="content">
					<VmdEditor
						v-model="form.content"
						height="400px"
						@change="updatePreview"
						placeholder="请输入文章内容..."
						:preview="false"
						mode="edit"
					/>
				</el-form-item>

				<el-form-item label="参考内容" prop="refContent">
					<div class="ref-content-display">
						<div v-if="form.refContent" v-html="form.refContent" class="ref-content-html"></div>
						<div v-else class="ref-content-empty">暂无参考内容</div>
					</div>
				</el-form-item>
			</el-form>
		</div>

		<!-- 右侧预览区域 -->
		<div class="editor-right">
			<div class="preview-content">
				<!-- 灰色框包围整个预览内容 -->
				<div class="preview-container">
					<h2 class="preview-title">{{ form.title || '文章标题' }}</h2>
					<div class="preview-meta">
						<span class="meta-item">作者：{{ form.author || '' }}</span>
						<span class="meta-item">日期：{{ form.createTime ? form.createTime.split(' ')[0] : '' }}</span>
					</div>

					<div class="preview-content-area">
						<div v-if="form.content" class="content-display">
							<VmdPreview :text="processedContent" />
						</div>
						<div v-else class="empty-content">
							<p>暂无内容，请在左侧编辑区域输入文章内容</p>
						</div>

						<div v-if="form.refContent" class="ref-content">
							<div class="ref-content-text" v-html="form.refContent">
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>

			<template #footer>
				<el-button @click="closeDialog" style="margin: 5px 10px;">关闭</el-button>
				<el-button type="primary" @click="handleSubmit(formRef)" style="margin: 5px 10px;">
					{{ isEdit ? '保存修改' : '提交' }}
				</el-button>
			</template>
		</el-dialog>
	</div>
</template>

<script setup lang="ts">
import { ElMessage } from "element-plus";
import {
	Edit, Delete, Search, Link
} from "@element-plus/icons-vue";
import { ref, reactive, onMounted, nextTick, computed } from "vue";
import { useCool } from "/@/cool";
import { removeEmptyFromObject } from "/@/cool/utils";
import { datelineToDate } from "/@/cool/utils";
import type { FormInstance } from "element-plus";
// @ts-ignore
import VMdEditor from '@kangc/v-md-editor';
// @ts-ignore
import VMdPreview from '@kangc/v-md-editor/lib/preview';
import '@kangc/v-md-editor/lib/style/base-editor.css';
import '@kangc/v-md-editor/lib/theme/style/github.css';
// @ts-ignore
import githubTheme from '@kangc/v-md-editor/lib/theme/github.js';
import hljs from 'highlight.js';

VMdEditor.use(githubTheme, {
	Hljs: hljs,
});
VMdPreview.use(githubTheme, {
	Hljs: hljs,
});

// 注册组件
const VmdEditor = VMdEditor;
const VmdPreview = VMdPreview;

const { service } = useCool();

const search = reactive({
	s: ""
});

const tableHeight = ref(0);
const tableData = ref([]);
const currentPage = ref(1);
const pageSize = ref(50);
const total = ref(0);

// 弹出框相关数据
const dialogVisible = ref(false);
const isEdit = ref(false);
const gettingInfo = ref(false);

const form = reactive({
	id: 0,
	url: "",
	title: "",
	board: "",
	author: "",
	channel: "",
	tag: "",
	content: "",
	refContent: "",
	createTime: "",
	fixNews: false
});

const rules = reactive({
	url: [{ required: true, message: "请输入URL", trigger: "blur" }],
	title: [{ required: true, message: "请输入标题", trigger: "blur" }]
});

const formRef = ref<FormInstance>();

// 处理图片域名的计算属性
const processedContent = computed(() => {
	if (!form.content) return '';

	// 处理图片路径，添加域名前缀
	return form.content.replace(
		/!\[([^\]]*)\]\((?!https?:\/\/)([^)]+)\)/g,
		'![$1](https://forum.chasedream.com/$2)'
	);
});



const formatter = (datetime: any) => {
	if (!datetime) return '-';
	return datelineToDate(new Date(datetime).getTime() / 1000, "YYYY-MM-DD HH:mm:ss");
};

// 获取URL显示文本：board字段 + / + 从URL中提取的数字
const getUrlDisplayText = (row: any) => {
	if (!row.url) return '-';

	// 从URL中提取数字，例如从 https://forum.chasedream.com/thread-1396377-1-1.html 提取 1396377
	const match = row.url.match(/thread-(\d+)/);
	const threadId = match ? match[1] : '';

	// 返回 board字段 + / + 提取的数字
	const board = row.board || '';
	return threadId ? `${board}/${threadId}` : (board || row.url);
};

// 更新预览内容
const updatePreview = () => {
	// 使用markdown编辑器，不需要额外处理
	// 保留此方法以兼容现有代码
};

const handleSizeChange = (val: number) => {
	pageSize.value = val;
	refresh();
};

const handleCurrentChange = (val: number) => {
	currentPage.value = val;
	refresh();
};

const editClick = (row: any) => {
	showEditDialog(row.id);
};

const deleteHandler = (row: any) => {
	service.base.common.portal
		.candidateArticleDelete({
			id: row.id
		})
		.then(() => {
			ElMessage({
				message: "已删除!",
				type: "success"
			});
			refresh();
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

const refresh = () => {
	const s = removeEmptyFromObject(search);

	service.base.common.portal
		.candidateArticlePage({
			...s,
			page: currentPage.value,
			pageSize: pageSize.value
		})
		.then((res) => {
			tableData.value = res[0];
			total.value = res[1];
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

// 弹出框控制方法
const showAddDialog = () => {
	isEdit.value = false;
	resetForm();
	// 添加模式时设置默认创建时间为当前时间
	form.createTime = new Date().toISOString().slice(0, 19).replace('T', ' ');
	dialogVisible.value = true;
};

const showEditDialog = (id: number) => {
	isEdit.value = true;
	resetForm();
	form.id = id;
	dialogVisible.value = true;
	nextTick(() => {
		loadData();
	});
};

const closeDialog = () => {
	dialogVisible.value = false;
	resetForm();
};

const resetForm = () => {
	Object.assign(form, {
		id: 0,
		url: "",
		title: "",
		board: "",
		author: "",
		channel: "",
		tag: "",
		content: "",
		refContent: "",
		createTime: "",
		fixNews: false
	});

	// 清除表单验证
	if (formRef.value) {
		formRef.value.clearValidate();
	}
};

// 加载编辑数据
const loadData = async () => {
	service.base.common.portal
		.candidateArticleFindOne({
			id: form.id
		})
		.then((res) => {
			Object.assign(form, res);
			updatePreview(); // 加载数据后更新预览
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

// 获取文章信息
const getArticleInfo = async () => {
	if (!form.url) {
		ElMessage.warning("请先输入URL");
		return;
	}

	gettingInfo.value = true;
	try {
		const data = await service.base.common.portal.getArticleInfo({ url: form.url });
		form.title = data.title || "";
		form.author = data.author || "";
		form.board = data.board || "";
		form.content = data.content || "";
		form.refContent = data.refContent || "";
		updatePreview();
		ElMessage.success("获取文章信息成功");
	} catch (err: any) {
		ElMessage.error(err.message || "获取文章信息失败");
	} finally {
		gettingInfo.value = false;
	}
};

// 访问URL
const visitUrl = () => {
	if (!form.url) {
		ElMessage.warning("请先输入URL");
		return;
	}
	window.open(form.url, '_blank');
};

// 表单提交
const handleSubmit = async (formEl: FormInstance | undefined) => {
	if (!formEl) return;

	await formEl.validate((isValid, _fields) => {
		if (!isValid) return;

		if (isEdit.value) {
			// 编辑模式
			service.base.common.portal
				.candidateArticleUpdate({
					...form
				})
				.then(() => {
					ElMessage({
						message: "已修改!",
						type: "success"
					});
					closeDialog();
					refresh();
				})
				.catch((err) => {
					ElMessage.error(err.message);
				});
		} else {
			// 添加模式
			service.base.common.portal
				.candidateArticleCreate({
					...form
				})
				.then(() => {
					ElMessage({
						message: "已添加!",
						type: "success"
					});
					closeDialog();
					refresh();
				})
				.catch((err) => {
					ElMessage.error(err.message);
				});
		}
	});
};

refresh();

onMounted(() => {
	nextTick(() => {
		tableHeight.value = window.innerHeight - 250;
		window.onresize = () => {
			tableHeight.value = window.innerHeight - 250;
		};
	});
});
</script>

<style scoped>
.url-input-group {
	display: flex;
	gap: 10px;
	align-items: center;
	width: 100%;
}

.url-input {
	flex: 1;
	min-width: 0;
}

.url-buttons {
	display: flex;
	gap: 8px;
	flex-shrink: 0;
	align-items: center;
}

.dialog-container {
	display: flex;
	height: 70vh;
	gap: 20px;
}

.editor-left {
	flex: 1;
	overflow-y: auto;
}

.editor-right {
	flex: 1;
	display: flex;
	flex-direction: column;
	border-left: 1px solid #e5e7eb;
	padding-left: 20px;
}

.preview-content {
	flex: 1;
	overflow-y: auto;
}

/* 预览容器 */
.preview-container {
	background: white;
	border: 1px solid #e5e7eb;
	border-radius: 8px;
	padding: 20px;
	flex: 1;
	overflow-y: auto;
}

.preview-title {
	margin: 0 0 15px 0;
	font-size: 24px;
	font-weight: 600;
	color: #1f2937;
	text-align: center;
}

.preview-meta {
	display: flex;
	justify-content: space-between;
	align-items: center;
	font-size: 14px;
	color: #6b7280;
	margin-bottom: 20px;
}

.meta-item {
	font-weight: 500;
	color: #ADADAD;
}

/* 内容区域，与作者和日期对齐 */
.preview-content-area {
	margin: 0;
	padding: 0;
}

.content-display {
	margin-bottom: 20px;
}

.ref-content {
	margin-top: 20px;
	padding-top: 20px;
	border-top: 1px solid #e5e7eb;
}



.ref-content-text {
	background: #f9fafb;
	border: 1px solid #e5e7eb;
	border-radius: 4px;
	padding: 15px;
	font-size: 14px;
	line-height: 1.6;
	color: #374151;
	min-height: 80px;
}

.empty-content {
	text-align: center;
	padding: 40px 20px;
	color: #9ca3af;
}

.empty-content p {
	margin: 0;
	font-size: 14px;
}

/* v-md-editor 生成的 markdown 内容样式 */
:deep(.github-markdown-body) {
	padding: 0 !important;
}

.github-markdown-body {
	padding: 0 !important;
}

/* 更强的选择器确保样式生效 */
.preview-container .github-markdown-body {
	padding: 0 !important;
}

.content-display .github-markdown-body {
	padding: 0 !important;
}
</style>





